# Repository Pattern Refactoring PRD

## Overview

This PRD analyzes how implementing the Android-recommended Repository pattern would change the current ConversationAgent architecture, comparing the current approach with the proposed refactoring.

## Current Architecture Analysis

### Current ConversationAgent Responsibilities (1722 lines)

#### **Conversation Scope Management**:
```kotlin
// Current: ConversationAgent owns conversation scope
private var conversationScope: CoroutineScope? = null

// Creates scope for core loop
conversationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)

// Creates scope for concept building (replaces existing)
conversationScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
```

#### **LLM Conversation Management**:
```kotlin
// Current: ConversationAgent handles all LLM interactions
suspend fun getNextBrainDecision(context: ConceptBuildingContext, toolLibrary: ConceptToolLibrary)
suspend fun progressCoreLoop()
private suspend fun handleContinueCheckIn(response: CheckInResponse)
```

#### **Tool Execution**:
```kotlin
// Current: ConversationAgent manages tool libraries
private val conceptToolLibrary: ConceptToolLibrary by lazy { createToolLibrary() }
private fun createToolLibrary(): ConceptToolLibrary
```

#### **State Management**:
```kotlin
// Current: ConversationAgent coordinates with AgentCortex
agentCortex.updateConversationType(ConversationType.ConceptBuilding)
agentCortex.updateCoreLoopState(newState)
```

#### **Voice Interaction**:
```kotlin
// Current: ConversationAgent handles TTS/STT
suspend fun speak(text: String)
fun processVoiceInput(input: String)
private fun changeDialogueState(newState: DialogueState)
```

#### **Navigation Coordination**:
```kotlin
// Current: ConversationAgent triggers navigation
withContext(Dispatchers.Main) {
    navigationManager.navigateToConceptScreen(wish.id)
}
```

## Proposed Repository Pattern Architecture

### New ConversationRepository

#### **Conversation Scope Ownership**:
```kotlin
class ConversationRepository @Inject constructor(
    private val brainService: BrainService,
    private val manifestationRepository: ManifestationRepository,
    private val conceptRepository: ConceptRepository
) {
    // Repository owns persistent conversation scope
    private val conversationScope = CoroutineScope(SupervisorJob() + Dispatchers.IO)
    private var currentConversationType: ConversationType? = null
    private var conversationState: ConversationState = ConversationState.Idle
    
    // Conversation lifecycle management
    fun startCoreLoopConversation(): Flow<ConversationEvent>
    fun transitionToConceptBuilding(wishId: Int): Flow<ConversationEvent>
    fun stopConversation()
    fun pauseConversation()
    fun resumeConversation()
}
```

#### **LLM Conversation Management**:
```kotlin
// Repository handles all LLM interactions
private suspend fun executeConversationLoop(
    conversationType: ConversationType,
    context: ConversationContext
): Flow<ConversationEvent> {
    return flow {
        while (conversationState == ConversationState.Active) {
            when (conversationType) {
                ConversationType.CoreLoop -> {
                    val guidance = brainService.getCoreLoopGuidance(createPrompt())
                    emit(ConversationEvent.SpeakText(guidance))
                    emit(ConversationEvent.WaitForInput)
                }
                ConversationType.ConceptBuilding -> {
                    val decision = brainService.getNextConceptAction(createPrompt())
                    val result = executeToolAction(decision)
                    emit(ConversationEvent.ToolResult(result))
                }
            }
        }
    }
}
```

#### **Tool Execution Management**:
```kotlin
// Repository manages tool libraries
private val coreLoopTools: CoreLoopToolLibrary by lazy { createCoreLoopTools() }
private val conceptTools: ConceptToolLibrary by lazy { createConceptTools() }

private suspend fun executeToolAction(decision: BrainDecision): ToolResult {
    return when (currentConversationType) {
        ConversationType.CoreLoop -> coreLoopTools.executeTool(decision)
        ConversationType.ConceptBuilding -> conceptTools.executeTool(decision)
        else -> ToolResult.Error("No active conversation")
    }
}
```

### Refactored ConversationAgent

#### **Reduced Responsibilities** (~800 lines estimated):
```kotlin
class ConversationAgent @Inject constructor(
    private val conversationRepository: ConversationRepository,
    private val agentCortex: AgentCortex,
    private val voiceService: VoiceService,
    private val navigationManager: NavigationManager
) {
    // No conversation scope ownership
    // No LLM interaction management
    // No tool execution
    
    // Focus on UI coordination only
}
```

#### **Voice Interaction Coordination**:
```kotlin
// ConversationAgent handles TTS/STT coordination
suspend fun speak(text: String) = voiceService.speak(text)
fun processVoiceInput(input: String) {
    // Delegate to repository
    conversationRepository.processUserInput(input)
}

private fun observeConversationEvents() {
    scope.launch {
        conversationRepository.conversationEvents.collect { event ->
            when (event) {
                is ConversationEvent.SpeakText -> speak(event.text)
                is ConversationEvent.WaitForInput -> changeDialogueState(DialogueState.ExpectingInput)
                is ConversationEvent.NavigateToScreen -> handleNavigation(event)
                is ConversationEvent.ToolResult -> handleToolResult(event)
            }
        }
    }
}
```

#### **Navigation Coordination**:
```kotlin
// ConversationAgent coordinates navigation based on repository events
private suspend fun handleNavigation(event: ConversationEvent.NavigateToScreen) {
    when (event.screen) {
        Screen.Concept -> {
            agentCortex.updateConversationType(ConversationType.ConceptBuilding)
            withContext(Dispatchers.Main) {
                navigationManager.navigateToConceptScreen(event.wishId)
            }
        }
    }
}
```

#### **State Coordination**:
```kotlin
// ConversationAgent coordinates UI state based on repository state
private fun observeConversationState() {
    scope.launch {
        conversationRepository.conversationState.collect { state ->
            agentCortex.updateConversationType(state.conversationType)
            changeDialogueState(state.toDialogueState())
        }
    }
}
```

## Comparison Analysis

### Current Architecture (Monolithic ConversationAgent)

#### **Pros**:
- ✅ **Simple to understand** - Everything in one place
- ✅ **Direct control** - No abstraction layers
- ✅ **Easy debugging** - Single component to trace
- ✅ **No over-engineering** - Matches current complexity level

#### **Cons**:
- ❌ **Mixed responsibilities** - UI + business logic + state management
- ❌ **Hard to test** - Tightly coupled components
- ❌ **Scope management issues** - Navigation boundary problems
- ❌ **Large file size** - 1722 lines, hard to maintain

### Repository Pattern Architecture

#### **Pros**:
- ✅ **Separation of concerns** - Clear responsibility boundaries
- ✅ **Testability** - Repository can be tested independently
- ✅ **Android best practices** - Follows recommended patterns
- ✅ **Persistent conversation state** - Survives navigation transitions
- ✅ **Cleaner ConversationAgent** - Focused on UI coordination

#### **Cons**:
- ❌ **Added complexity** - More files, more abstraction
- ❌ **Event-driven complexity** - Flow/Event patterns to learn
- ❌ **Potential over-engineering** - May be overkill for current needs
- ❌ **Migration effort** - Significant refactoring required

## Implementation Effort Analysis

### Current Integration (Simple Approach)
**Estimated effort**: 2-3 hours
- Add concept screen launch to `handleStructuredTransition()`
- Handle scope continuation in `observeConceptViewModel()`
- Test basic flow

### Repository Pattern Refactoring
**Estimated effort**: 2-3 days
- Create ConversationRepository with event system
- Migrate conversation logic from ConversationAgent
- Create ConversationEvent/ConversationState system
- Update ConversationAgent to use repository
- Migrate tool libraries to repository
- Update all calling code
- Test entire conversation flow
- Debug event-driven interactions

## Recommendation

### For MVP/Current State:
**Use Simple Integration Approach**
- Complete the concept screen integration with minimal changes
- Get working functionality first
- Defer architectural improvements until after MVP

### For Future Refactoring:
**Consider Repository Pattern Post-MVP**
- Once conversation flows are stable and complete
- When testing becomes more important
- If conversation complexity grows significantly
- When multiple conversation types need better management

The Repository pattern is architecturally superior but may be premature optimization for the current incomplete state of the conversation system.
