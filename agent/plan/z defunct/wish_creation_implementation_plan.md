# WishCreationManager Implementation Plan
# 📋 FUNCTION: Step-by-step implementation guide based on actual current state

## Current Reality Check

### ✅ WHAT EXISTS (Working Components)
- **BrainService.generateSimpleText()** - LLM integration method ✅
- **WishUtilities** - Centralized database operations ✅
- **AgentCortex WishCreationState** - State management properties ✅
- **TransitionChain** - Routes to WISH_COLLECTION when slots available ✅
- **ConversationAgent.navigateCoreLoopPhase()** - Core loop routing ✅

### ❌ WHAT'S MISSING (Critical Gaps)
- **WishCreationManager.kt** - Main class file ❌
- **processWishCreation()** - Main orchestration method ❌
- **WishCreationResult & WishCreationState** - Data classes ❌
- **generateInitialWish() & refineWish()** - LLM integration methods ❌
- **WISH_COLLECTION case** in navigateCoreLoopPhase() ❌
- **launchWishCreation()** method in ConversationAgent ❌

### 🔍 WHAT'S INCONSISTENT (Documentation vs Reality)
- Documentation claims WishCreationManager exists but file is missing
- References to methods that don't exist in actual codebase
- Implementation status unclear across multiple documents

---

## Implementation Strategy

### **Phase 1: Core Infrastructure (Priority 1)**

#### Task 1.1: Create Data Structures
**Location:** `app/src/main/java/com/example/voxmanifestorapp/ui/agent/wishcreation/WishCreationManager.kt`

```kotlin
data class WishCreationResult(
    val speechResponse: String,
    val shouldContinue: Boolean,
    val isComplete: Boolean = false
)

data class WishCreationState(
    val isActive: Boolean = false,
    val currentWish: String? = null,
    val stage: String = "generating"
)
```

**Success Criteria:** Data classes compile and can be used in ConversationAgent

#### Task 1.2: Create WishCreationManager Class
**Location:** `app/src/main/java/com/example/voxmanifestorapp/ui/agent/wishcreation/WishCreationManager.kt`

```kotlin
class WishCreationManager(
    private val wishUtilities: WishUtilities,
    private val brainService: BrainService
) {
    suspend fun processWishCreation(
        themes: List<ConversationalTheme>, 
        userInput: String?, 
        currentState: WishCreationState
    ): WishCreationResult
    
    private suspend fun generateInitialWish(themes: List<ConversationalTheme>): String
    private suspend fun refineWish(currentWish: String, userFeedback: String): String
}
```

**Success Criteria:** Class compiles and can be instantiated in ConversationAgent

### **Phase 2: State Machine Implementation (Priority 2)**

#### Task 2.1: Implement processWishCreation() Method
**Logic:** Handle state machine transitions ("generating" → "refining" → "validating")

```kotlin
suspend fun processWishCreation(
    themes: List<ConversationalTheme>, 
    userInput: String?, 
    currentState: WishCreationState
): WishCreationResult {
    return when (currentState.stage) {
        "generating" -> {
            val initialWish = generateInitialWish(themes)
            WishCreationResult(
                speechResponse = "I've created a wish based on our conversation: '$initialWish'. What do you think?",
                shouldContinue = true,
                isComplete = false
            )
        }
        "refining" -> {
            val refinedWish = refineWish(currentState.currentWish ?: "", userInput ?: "")
            WishCreationResult(
                speechResponse = "Here's the refined wish: '$refinedWish'. Is this what you're looking for?",
                shouldContinue = true,
                isComplete = false
            )
        }
        "validating" -> {
            // Handle yes/no confirmation
            if (userInput?.contains("yes", ignoreCase = true) == true) {
                // Save wish to database
                val slot = wishUtilities.findNextEmptyWishSlot()
                wishUtilities.saveWishToSlot(currentState.currentWish ?: "", slot)
                WishCreationResult(
                    speechResponse = "Perfect! Your wish has been saved. Let's continue with our conversation.",
                    shouldContinue = false,
                    isComplete = true
                )
            } else {
                // Continue refining
                WishCreationResult(
                    speechResponse = "Let me refine it further. What would you like to change?",
                    shouldContinue = true,
                    isComplete = false
                )
            }
        }
        else -> {
            WishCreationResult(
                speechResponse = "I'm not sure what to do. Let's start over.",
                shouldContinue = false,
                isComplete = false
            )
        }
    }
}
```

**Success Criteria:** Method handles all state transitions correctly

#### Task 2.2: Implement LLM Integration Methods
**generateInitialWish():**
```kotlin
private suspend fun generateInitialWish(themes: List<ConversationalTheme>): String {
    val themeText = themes.joinToString("\n") { "- ${it.title}: ${it.observations.joinToString(", ")}" }
    
    val prompt = """
        Analyze these conversation themes and create a specific, present-tense wish:
        
        $themeText
        
        Requirements:
        - Present tense, positive language
        - Specific and actionable
        - Emotionally resonant
        - Based on the themes provided
        
        Return only the wish text, no additional explanation.
    """.trimIndent()
    
    return brainService.generateSimpleText(prompt).getOrElse { 
        "I want to create positive change in my life" 
    }
}
```

**refineWish():**
```kotlin
private suspend fun refineWish(currentWish: String, userFeedback: String): String {
    val prompt = """
        Current wish: $currentWish
        User feedback: $userFeedback
        
        Refine the wish based on feedback while maintaining:
        - Present tense, positive language
        - Specificity and clarity
        - Core intention from original wish
        
        Return only the refined wish text, no additional explanation.
    """.trimIndent()
    
    return brainService.generateSimpleText(prompt).getOrElse { currentWish }
}
```

**Success Criteria:** Both methods return coherent wish text from LLM

### **Phase 3: ConversationAgent Integration (Priority 3)**

#### Task 3.1: Add WishCreationManager Instance
**Location:** `ConversationAgent.kt` constructor

```kotlin
private val wishUtilities = WishUtilities(repository)
private val wishCreationManager = WishCreationManager(wishUtilities, brainService)
```

**Success Criteria:** ConversationAgent can instantiate WishCreationManager

#### Task 3.2: Add WISH_COLLECTION Case
**Location:** `ConversationAgent.navigateCoreLoopPhase()` when statement

```kotlin
when (targetPhase) {
    ConversationPhase.PRESENT_STATE_EXPLORATION,
    ConversationPhase.DESIRED_STATE_EXPLORATION -> {
        launchConceptScreenForPhase(actionPlan.targetWishId, targetPhase)
    }
    ConversationPhase.WISH_COLLECTION -> {
        launchWishCreation(actionPlan.themes)
    }
    else -> {
        // Continue with main conversation flow for other phases
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    }
}
```

**Success Criteria:** WISH_COLLECTION routes to wish creation instead of falling through

#### Task 3.3: Implement launchWishCreation() Method
**Location:** `ConversationAgent.kt`

```kotlin
private suspend fun launchWishCreation(themes: List<ConversationalTheme>) {
    logStatus("🎯 WISH CREATION: Starting conversational wish creation with ${themes.size} themes", StatusColor.Go)
    
    // Initialize wish creation state
    agentCortex.updateWishCreationState(WishCreationState(
        isActive = true,
        stage = "generating"
    ))
    
    // Set conversation type for wish creation
    agentCortex.updateConversationType(ConversationType.WishCreation)
    
    // Process initial wish generation
    val currentState = agentCortex.wishCreationState.value
    val result = wishCreationManager.processWishCreation(themes, null, currentState)
    
    if (result.shouldContinue) {
        // Update state with generated wish
        agentCortex.updateWishCreationState(currentState.copy(
            currentWish = result.speechResponse.substringAfter("'").substringBefore("'"),
            stage = "refining"
        ))
        
        // Speak the generated wish
        speak(result.speechResponse)
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.FREEFORM))
    } else {
        // Handle error or completion
        logStatus("⚠️ Wish creation failed to start", StatusColor.Pause)
        changeDialogueState(DialogueState.ExpectingInput(VoxInputType.BRAIN_RESPONSE))
    }
}
```

**Success Criteria:** Method initializes wish creation and speaks initial wish

### **Phase 4: Testing & Verification (Priority 4)**

#### Task 4.1: Test Complete Flow
1. **Check-in conversation** → Extract themes
2. **TransitionChain** → Route to WISH_COLLECTION
3. **navigateCoreLoopPhase()** → Call launchWishCreation()
4. **WishCreationManager** → Generate initial wish
5. **User feedback** → Refine wish
6. **Final confirmation** → Save to database
7. **Return to core loop** → Continue conversation

**Success Criteria:** End-to-end flow works without errors

#### Task 4.2: Error Handling Verification
- Test with empty themes list
- Test with LLM failure
- Test with database save failure
- Test with invalid user input

**Success Criteria:** All error cases handled gracefully

---

## Implementation Order

1. **Task 1.1** - Create data structures (foundation)
2. **Task 1.2** - Create WishCreationManager class (core)
3. **Task 2.1** - Implement processWishCreation() (logic)
4. **Task 2.2** - Implement LLM methods (integration)
5. **Task 3.1** - Add to ConversationAgent (connection)
6. **Task 3.2** - Add WISH_COLLECTION case (routing)
7. **Task 3.3** - Implement launchWishCreation() (orchestration)
8. **Task 4.1** - Test complete flow (verification)
9. **Task 4.2** - Error handling (robustness)

---

## Success Criteria Summary

- [x] WishCreationManager.kt exists and compiles
- [x] processWishCreation() handles all state transitions
- [x] LLM integration methods return coherent wish text
- [ ] ConversationAgent can instantiate and use WishCreationManager
- [ ] WISH_COLLECTION case routes to wish creation
- [ ] launchWishCreation() method initializes wish creation process
- [ ] End-to-end flow from check-in to saved wish works
- [ ] Error handling is robust and graceful

---

## Notes

- **Keep it simple** - Focus on MVP functionality
- **Reuse patterns** - Follow existing codebase conventions
- **Test incrementally** - Verify each component before moving to next
- **Document as we go** - Update scratchpad with progress

---

## Code Quality Notes

### ⚠️ BRAINSERVICE FUNCTION NAME ISSUE
**Problem:** `BrainService.generateSimpleText()` is a poor function name
- **Issues:** Vague, uninformative, suggests poor code quality  
- **Action Required:** Rename to something more descriptive like `generateTextFromPrompt()` or `generateContentFromPrompt()`
- **Timing:** Review and refactor after initial implementation is working
- **Scope:** Check all existing usage of this function for quality issues

### 🔍 EXISTING CODE REVIEW NEEDED
**Scope:** Review BrainService and related LLM integration code
**Purpose:** Ensure code quality standards are met before integration
**Timing:** After WishCreationManager implementation is functional 