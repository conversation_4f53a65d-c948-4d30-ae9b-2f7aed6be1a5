# VoxManifestorApp MVP Launch PRD
*Product Requirements Document*

## Document Purpose & Overview

This Product Requirements Document (PRD) defines the scope, requirements, and plan for launching the Minimum Viable Product (MVP) of VoxManifestorApp. It focuses on essential features and critical fixes needed for a successful initial release.

### PRD Function
- Define MVP scope and success criteria.
- Outline critical development tasks and architectural necessities.
- Track progress towards MVP launch.

---

## Table of Contents
1. [MVP Vision & Success Criteria](#mvp-vision--success-criteria)
2. [User Interaction Mode (MVP Focus)](#user-interaction-mode-mvp-focus)
3. [Critical Blocking Issues](#critical-blocking-issues)
4. [Essential Architecture Improvements (MVP)](#essential-architecture-improvements-mvp)
5. [MVP Feature Requirements](#mvp-feature-requirements)
6. [Development Roadmap (MVP)](#development-roadmap-mvp)
7. [Launch Readiness Criteria](#launch-readiness-criteria)

---

## MVP Vision & Success Criteria

### Core MVP Value Proposition

The VoxManifestor MVP will deliver a foundational voice-first conversational experience that:
1.  **Initiates reflective dialogue** through a guided Check-In, allowing users to voice current thoughts and set a focus for the session.
2.  **Facilitates focused intention** by guiding the user to define and modify up to five primary Wishes.
3.  **Enables profound clarity** through fluent, interactive discussions, helping users to clearly visualize, describe, and have stored their Present and Desired States for each Wish.
4.  **Promotes actionable progress** by assisting the user in conversationally identifying and storing a single, clear 'Next Step' for each chosen Wish, as part of an initial Pathways system.

### Success Metrics (High-Level for MVP)
- [ ] Core Loop (Check-in, management of up to 5 Wishes including detailed P/D State conceptualization and Next Step ID for each) is functional and users can complete this cycle.
- [ ] Voice interaction is fluent and supports the depth of discussion required for P/D State and Next Step identification.
- [ ] System stability during core MVP flows for all 5 wishes.
- [ ] User feedback indicates the app provides significant value in clarifying wishes, understanding their current/desired realities, and identifying actionable next steps.

### MVP Scope Boundaries
**IN SCOPE (MVP):**
- [ ] Functional Check-in system (DialogueChain architecture for Phase 1).
- [ ] **Define and modify up to five primary Wishes.**
- [ ] **Fluent and interactive discussion enabling clear visualization, description, and storage of Present and Desired States for each Wish.**
- [ ] **Initial Pathways system: conversational identification and storage of one Next Step between Present and Desired State for each Wish.**
- [ ] Core Loop: CHECK_IN, WISH_MANAGEMENT (up to 5), detailed PRESENT/DESIRED_STATE_CONCEPTUALIZATION (for each wish), NEXT_STEP_IDENTIFICATION (for each wish).
- [ ] Agent maintains context across these Core Loop phases and relevant screen changes for all active wishes.
- [ ] Persistent Status Bar (simple version).

**OUT OF SCOPE (Post-MVP):**
- **Full Conversational Navigation System**: Complex branching conversations for phase selection (MVP will have minimal conversational choice)
- **Advanced Present/Desired State Summarization**: LLM-based summarization of large concept data (similar to theme summarization)
- **Enhanced Storage Capacity**: Expanded storage and management for detailed present/desired state discussions
- **Automated Core Loop Progression**: Intelligent phase completion tracking and automatic cycling through all phases for all wishes (architectural foundation exists but implementation deferred)
- Advanced conversation branching, vector database/long-term memory, full command-driven mode, advanced analytics, detailed UI gamification beyond basic check-in timer, detailed multi-step pathway mapping, visualization/affirmation sessions, blockage identification.

---

## User Interaction Mode (MVP Focus)

### Conversational Mode (Core Loop)
**Description:**
- The primary interaction flow for the MVP.
- Centered around the agent's dialog system and the Core Loop process:
    - Check-in.
    - Management of up to five Wishes.
    - Detailed Present/Desired State conceptualization for each Wish.
    - Next Step identification for each Wish.
- Leverages the language model to guide users through fluent, interactive conversations for these tasks.

**MVP Implementation Status:**
- [✅] DialogueChain 3-chain architecture for Check-in implemented and operational.
- [✅] TransitionChain with structured action suggestions implemented.
- [🔄] IN PROGRESS: Auto-transition from check-in to core loop phases (current focus).
- [🔄] IN PROGRESS: Concept screen modularization and integration with core loop.
- [ ] To Be Implemented: Agent logic and UI support for managing up to 5 Wishes.
- [ ] To Be Implemented: Agent logic for fluent, interactive discussions for detailed Present/Desired State conceptualization (per Wish) and storage.
- [ ] To Be Implemented: Agent logic for conversational identification of a Next Step (per Wish) and storage.
- [ ] To Be Implemented: Robust agent context management across screens/phases for all active wishes and their states/next steps.

*(Note: A command-driven administrative mode exists conceptually but is out of scope for MVP.)*

---

## Critical Blocking Issues

### High Priority (Blocking MVP Launch)

#### 1. Check-In to Core Loop Transition *[CRITICAL - IN PROGRESS]*
**Problem:** Need seamless transition from check-in TransitionChain output to appropriate core loop phases and concept screen interactions.
**Impact:** Users cannot progress from check-in to actual manifestation work.
**Solution Requirements:**
  - [🔄] IN PROGRESS: Implement auto-transition from TransitionChain suggestions to core loop phases.
  - [🔄] IN PROGRESS: Modularize concept screen functions and integrate with core loop.
  - [ ] Ensure conversation context and enhanced wish data flow through transitions.
**Success Criteria:**
  - [ ] Seamless flow from check-in completion to active concept screen conversation.
  - [ ] Preserved conversation context across phase transitions.

#### 2. Multi-Wish Management System *[HIGH]*
**Problem:** Need agent logic and UI support for managing up to 5 wishes with their associated present/desired states and next steps.
**Impact:** Core MVP functionality cannot be demonstrated without multi-wish capability.
**Solution Requirements:**
  - [ ] Implement wish creation, selection, and modification logic.
  - [ ] Build UI support for managing multiple wishes and their states.
  - [ ] Ensure agent can navigate between wishes and maintain context.

---

## Essential Architecture Improvements (MVP)

*(This section is for architecture work critical for MVP stability/functionality only.)*

- [🔄] **Check-In to Core Loop Integration**: Complete the transition from TransitionChain output to concept screen interactions with proper modularization.
- [ ] **Multi-Wish State Management**: Ensure `ConversationAgent` and `AgentCortex` can robustly maintain context for up to 5 wishes and their associated present/desired states and next steps.
- [ ] **Concept Screen Modularization**: Extract legacy concept building functions from ConversationAgent into dedicated modules while preserving functionality.

*(Major refactoring beyond concept screen extraction is post-MVP unless blocking core functionality.)*

---

## MVP Feature Requirements

### Core Features (Must Have for MVP)
- [✅] **Functional Check-In System (Core Loop - Phase 1)**
  - [✅] DialogueChain architecture for Check-in operational and stable.
  - [🔄] IN PROGRESS: Reliable conversational transition from Check-in to Core Loop phases.
  - [ ] **Check-In Timer UI (Simplified)**: Basic 5-minute session visual progress (e.g., text `MM:SS / 05:00`).

- [ ] **Core Loop - Wish Management, State Conceptualization, & Next Step Identification (Phases 2-4)**
  - [ ] **Wish Management (up to 5 Wishes)**: Agent facilitates definition, review, and modification of up to five primary Wishes.
  - [ ] **Present/Desired State Conceptualization (per Wish)**: Agent facilitates fluent, interactive discussion for users to clearly visualize, describe, and have stored their Present and Desired States for each active Wish. UI must support viewing/entering this information.
  - [ ] **Next Step Identification (per Wish)**: Agent facilitates conversational identification of a single, actionable Next Step for each primary Wish, derived from P/D state discussion. UI must support viewing this.
  - [ ] Agent maintains context and guides transitions smoothly through these phases for all active wishes.

- [ ] **Voice Processing Pipeline**
  - [ ] Foundational voice input/output capabilities are stable and performant for fluent conversations.
  - [ ] Basic error handling for network/API failures during voice interaction.

- [ ] **Data Persistence (for MVP Scope)**
  - [ ] User's (up to five) Wishes, their detailed Present/Desired states, and identified 'Next Steps' are stored and retrievable across sessions.
  - [ ] Conversation history for the current session visible.

- [ ] **Persistent Status Bar (Simplified)**
  - [ ] Bottom-screen text indicator showing current Core Loop phase and active Wish if applicable (e.g., "Check-In", "Wish: Career - P/D States", "Wish: Career - Next Step").

---

## Development Roadmap (MVP)

### Phase 1: Core Loop Integration & Concept Screen Modularization (Weeks 1-2)
**Goal:** Complete check-in to core loop transition and modularize concept screen functions for MVP foundation.
**Tasks:**
- [🔄] IN PROGRESS: Complete auto-transition from TransitionChain to core loop phases.
- [🔄] IN PROGRESS: Extract and modularize concept screen functions from ConversationAgent.
- [ ] Implement seamless conversation flow from check-in themes to concept screen interactions.
- [ ] Ensure enhanced wish data flows through transition process.
- [ ] Test end-to-end flow: Check-in → TransitionChain → Core Loop → Concept Screen.
**Success Criteria:**
- [ ] Seamless transition from check-in completion to active concept screen conversation.
- [ ] Concept screen functions cleanly separated into dedicated modules.
- [ ] Conversation context preserved throughout transition flow.

### Phase 2: Multi-Wish Management & Core Loop Implementation (Weeks 3-4)
**Goal:** Implement agent logic and UI support for managing up to 5 wishes through Present/Desired State conceptualization and Next Step identification.
**Tasks:**
- [ ] Design and implement agent logic & UI for managing up to 5 Wishes (create, select, view, modify basic details).
- [ ] Develop agent conversational flows for detailed Present/Desired State discussions for each Wish.
- [ ] Develop agent conversational flows for Next Step identification for each Wish.
- [ ] Ensure robust data persistence for all 5 wishes, their P/D states, and Next Steps.
- [ ] Implement agent context management to handle multiple wishes within the Core Loop.
- [ ] Add minimal conversational navigation for user choice in next actions.
**Success Criteria:**
- [ ] User can define up to 5 wishes, and for each, engage in detailed P/D state discussions and identify a Next Step with agent guidance.
- [ ] All data for 5 wishes (P/D states, Next Steps) persists correctly.
- [ ] Users have meaningful choice in what to work on next through conversational interaction.

### Phase 3: UI/UX Polish & Final Testing (Weeks 5-6)
**Goal:** Ensure fluent voice interaction, polish UI for multi-wish context, and complete final testing.
**Tasks:**
- [ ] Intensive testing and refinement of voice input/output for all MVP Core Loop interactions.
- [ ] UI polish for screens involved in managing 5 wishes and their details (P/D states, Next Steps).
- [ ] Implement simplified Check-In Timer UI and dynamic Persistent Status Bar.
- [ ] End-to-end testing of all Core Loop paths with 1 to 5 wishes defined.
- [ ] Edge case testing (e.g., modifying wishes, incomplete P/D states, changing next steps).
- [ ] Stability and performance testing with multiple wishes and extended conversations.
**Success Criteria:**
- [ ] Core Loop for up to 5 wishes is fully navigable via fluent voice interaction and clear UI.
- [ ] MVP core workflow is robust and reliable for up to 5 wishes.
- [ ] No critical bugs in the MVP scope that hinder core functionality.

---

## Launch Readiness Criteria

### Technical Readiness
- [✅] Check-in DialogueChain system operational and stable.
- [🔄] Check-in to Core Loop transition integration in progress.
- [ ] MVP Core Features (Check-in, 5-Wish Management with detailed P/D State & Next Step ID) fully implemented and tested.
- [ ] Voice pipeline supports fluent conversation for these features.
- [ ] Data persistence for up to 5 wishes and their components is working reliably.

### User Experience Readiness (MVP)
- [ ] Users can successfully navigate the Core Loop for one or more wishes (up to 5).
- [ ] Users can clearly define Wishes, articulate detailed Present/Desired States, and identify a Next Step for each, with agent support.
- [ ] Voice interaction is the primary and effective means of achieving these tasks.

---

## Post-MVP Priority Features

### Enhanced Present/Desired State Management *[HIGH PRIORITY POST-MVP]*

**Value Proposition**: Scalable management of detailed concept discussions with intelligent summarization.

**Feature Description**:
- LLM-based summarization of large amounts of present/desired state information (similar to theme summarization)
- Enhanced storage capacity for detailed concept discussions
- Intelligent context management for extensive manifestation conversations

**Implementation Points**:
- Summarization system similar to current theme extraction
- Expanded data structures for concept storage
- Context preservation across extended conversations

**Why Post-MVP**: Requires significant data architecture changes and LLM integration complexity.

### Advanced Conversational Navigation *[MEDIUM PRIORITY POST-MVP]*

**Value Proposition**: Full user agency in directing manifestation journey through natural conversation.

**Feature Description**:
- Complex branching conversations for phase selection beyond minimal MVP choice
- Advanced disagreement handling and alternative suggestion systems
- Sophisticated interruption and redirection capabilities

**Why Post-MVP**: Adds significant conversation flow complexity while MVP demonstrates core value with simpler navigation.

---
*This PRD will be updated as MVP development progresses. Last updated: [Current Date]*