# MVP July Launch v1.1 - Product Requirements Document

## Background and Motivation

Following a strategic review to meet a two-week deadline, we are shifting to a **hyper-focused MVP approach**. This revised plan prioritizes the most critical, high-value features that deliver a complete and demonstrable user journey. The core strengths of the check-in system, theme extraction, and conversational interaction remain the foundation.

**Strategic Focus**: Create a workable, polished, and downloadable app that one close friend can start using, establishing the foundation for user feedback and iterative development.


## MVP SCOPE DEFINITION

### **MVP Features (Current Implementation Focus)**

1. **Phase 1: CHECK-IN [MVP]** ✅
   - Opening stage: Voice journaling and theme extraction
   - Transition stage: Theme presentation and phase selection
   - Fully implemented with DialogueChain and TransitionChain

2. **Theme Visualization System [MVP]**
   - Display extracted themes in organized format
   - View themes from previous sessions
   - Success Criteria: User can review and understand conversation themes

3. **Conversation Replay & Summarization [MVP]**
   - "Repeat last response" functionality
   - "Summarize themes" voice command
   - Success Criteria: User can request spoken summary of themes

## Path to Immediate Release (Check-In Focus)

This section outlines the final tasks required to deliver a polished, stable release focused exclusively on the **Check-In and Theme Review** experience.

### 1. Define Conversation End Point
- **Task**: Explicitly define the end of the Check-In flow. After presenting the themes, the agent should provide a summary and gracefully conclude the conversation, returning to an idle state.
- **Example Script**: "I've analyzed our conversation and identified these key themes... This gives us a great foundation to work from. Let me know when you're ready to check in again."
- **Goal**: Create a clear and satisfying conclusion to the core loop, leaving the user with a sense of completion.

### 2. Stabilize the Core Loop
- **Task**: Temporarily disable the transition to `WISH_COLLECTION` in `TransitionChain.kt`. The agent should guide the user through the check-in and theme review, but not attempt to start the new conversational wish creation flow.
- **Goal**: Ensure the app provides a complete, end-to-end experience without hitting the known bug in the `ConversationAgent`'s handling of the wish creation loop.

### 3. Verify `CommandMode` Wish Management
- **Task**: Confirm that the existing `CommandMode` for wish management (add, edit, delete wishes via voice commands) is functional and polished.
- **Items**:
    - Test the voice commands for selecting, adding, and changing wishes.
    - Ensure the UI feedback is clear.
- **Goal**: Retain the app's core goal-management utility, providing a valuable, stable feature alongside the Check-In process.

### 4. Final UI/UX Polish
- **Task**: Perform a final review of the main screen and conversation interface.
- **Items**:
    - Ensure text is readable and well-formatted.
    - Check for any visual glitches or alignment issues.
    - Verify that the microphone state and agent status are always clear to the user.
- **Goal**: Create a clean, professional, and intuitive user interface.

### 5. Add Simple Onboarding Guidance
- **Task**: On the first app launch, have the agent provide a brief, one-time spoken introduction.
- **Example Script**: "Welcome to Vox Manifestor. To begin, just press the microphone button and tell me what's on your mind. When you're done, press it again, and I'll help you find the themes in your thoughts."
- **Goal**: Ensure a new user understands how to start their first session without confusion.

### 6. Robust Error Handling
- **Task**: Test and confirm graceful handling of common errors.
- **Scenarios**:
    - No network connection.
    - LLM API errors or timeouts.
    - Microphone permission denied.
- **Goal**: The app should recover gracefully and provide clear feedback to the user when an error occurs, rather than crashing or freezing.


### **POST-MVP Features (Deferred)**

*All POST-MVP features and deferred development items have been moved to `agent/plan/z defunct/POST_MVP_MANUALv2.0.md` for better organization and focus on MVP delivery.*

