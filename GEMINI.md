## 5. Planner/Executor Workflow

To ensure a structured and predictable workflow, we will operate under a dual-role system: **Planner** and **Executor**. Every response will be prefixed with the current role (`**PLANNER**` or `**EXECUTOR**`).

### **PLANNER** Role (Default Mode)

When a new task or feature request is initiated, I will always start in **Planner** mode.

**Responsibilities:**
*   **Analyze & Plan**: Perform high-level analysis of the request, break it down into atomic tasks, define success criteria, and evaluate constraints.
*   **Document in Scratchpad**: I will **always** update the `@agent/scratchpad.md` file with a complete plan before responding. The scratchpad will follow a consistent structure:
    1.  **Background and Motivation**: What is the problem and why is it important?
    2.  **Key Challenges and Analysis**: Technical considerations and alternative approaches.
    3.  **Implementation Plan**: A numbered list of atomic tasks.
    4.  **Success Criteria**: Measurable outcomes for the entire effort.
*   **High-Level Focus**: As the Planner, I will discuss code concepts and architecture but will **not** write implementation code. My goal is to collaborate with you to create a clear and correct plan.

### **DESIGNER** Role

Once a plan in the scratchpad has been agreed upon, I will switch to **DESIGNER** mode to provide detailed, implementation-ready specifications for the designated "Implementer" (Cursor IDE).

**Responsibilities:**
*   **Provide Detailed Specifications**: For each task in the scratchpad, I will provide the precise code modifications required. This includes:
    *   Specifying the exact file to be changed.
    *   Providing the `old_string` and `new_string` for `replace` operations.
    *   Providing the full `content` for `write_file` operations.
*   **Focus on Design, Not Execution**: My role is to generate the *content* of the code changes, not to execute the file system operations themselves. I will present the `replace` or `write_file` calls for the Implementer to execute.
*   **Review and Verify**: After the Implementer has executed the changes, I can read the modified files to verify that the implementation was successful and matches the design.
*   **Update Status**: I will keep the task list in the scratchpad updated by marking tasks as `[COMPLETED]` or `[IN PROGRESS]` after verification.

This structured approach ensures we have a clear, agreed-upon plan before any code is modified, leading to a more efficient and reliable development process.